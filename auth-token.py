import requests

def signin(email, password):
    url = "https://turbotable.ai/auth/sign-in"

    payload = {
    '1_email': email,
    '1_password': password,
    '0': '["$K1"]'
    }

    headers = {
    'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    'Accept': "text/x-component",
    'Accept-Encoding': "gzip, deflate, br, zstd",
    'next-action': "7f58d7c6ddcbba615cb17a4c36b027b1e80c9c6a62",
    'origin': "https://turbotable.ai",
    'referer': "https://turbotable.ai/auth/sign-in"
    }

    response = requests.post(url, data=payload, headers=headers)
    response.raise_for_status()
    auth_token = response.cookies.get("sb-exdfneumdxgoicwzmxjb-auth-token")
    print(auth_token)
    return auth_token

if __name__ == "__main__":
    email = "<EMAIL>"
    password = "xxxx"
    signin(email, password)