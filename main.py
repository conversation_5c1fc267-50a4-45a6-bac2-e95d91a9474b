import json
import os
import time
import uuid
import threading
from typing import Any, Dict, List, Optional, TypedDict, Union

import requests
from fastapi import Fast<PERSON><PERSON>, HTTPException, Depends, Query
from fastapi.responses import StreamingResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field


# TurboTable Account Management
class TurboTableAccount(TypedDict):
    auth_token: str
    chat_id: int
    createNewChat: str


# Global variables
VALID_CLIENT_KEYS: set = set()
TURBOTABLE_ACCOUNTS: List[TurboTableAccount] = []
TURBOTABLE_MODELS: List[Dict[str, Any]] = []
account_rotation_lock = threading.Lock()
account_index = 0
DEBUG_MODE = os.environ.get("DEBUG_MODE", "false").lower() == "true"


# Pydantic Models
class ChatMessage(BaseModel):
    role: str
    content: Union[str, List[Dict[str, Any]]]


class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[ChatMessage]
    stream: bool = True
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None
    top_p: Optional[float] = None


class ModelInfo(BaseModel):
    id: str
    object: str = "model"
    created: int
    owned_by: str


class ModelList(BaseModel):
    object: str = "list"
    data: List[ModelInfo]


class ChatCompletionChoice(BaseModel):
    message: ChatMessage
    index: int = 0
    finish_reason: str = "stop"


class ChatCompletionResponse(BaseModel):
    id: str = Field(default_factory=lambda: f"chatcmpl-{uuid.uuid4().hex}")
    object: str = "chat.completion"
    created: int = Field(default_factory=lambda: int(time.time()))
    model: str
    choices: List[ChatCompletionChoice]
    usage: Dict[str, int] = Field(
        default_factory=lambda: {
            "prompt_tokens": 0,
            "completion_tokens": 0,
            "total_tokens": 0,
        }
    )


class StreamChoice(BaseModel):
    delta: Dict[str, Any] = Field(default_factory=dict)
    index: int = 0
    finish_reason: Optional[str] = None


class StreamResponse(BaseModel):
    id: str = Field(default_factory=lambda: f"chatcmpl-{uuid.uuid4().hex}")
    object: str = "chat.completion.chunk"
    created: int = Field(default_factory=lambda: int(time.time()))
    model: str
    choices: List[StreamChoice]


# FastAPI App
app = FastAPI(title="TurboTable OpenAI API Adapter")
security = HTTPBearer(auto_error=False)


def log_debug(message: str):
    """Debug日志函数"""
    if DEBUG_MODE:
        print(f"[DEBUG] {message}")


def load_client_api_keys():
    """Load client API keys from client_api_keys.json"""
    global VALID_CLIENT_KEYS
    try:
        with open("client_api_keys.json", "r", encoding="utf-8") as f:
            keys = json.load(f)
            VALID_CLIENT_KEYS = set(keys) if isinstance(keys, list) else set()
            print(f"Successfully loaded {len(VALID_CLIENT_KEYS)} client API keys.")
    except FileNotFoundError:
        print("Error: client_api_keys.json not found. Client authentication will fail.")
        VALID_CLIENT_KEYS = set()
    except Exception as e:
        print(f"Error loading client_api_keys.json: {e}")
        VALID_CLIENT_KEYS = set()


def load_turbotable_accounts():
    """Load TurboTable accounts from turbotable.json"""
    global TURBOTABLE_ACCOUNTS
    TURBOTABLE_ACCOUNTS = []
    try:
        with open("turbotable.json", "r", encoding="utf-8") as f:
            accounts = json.load(f)
            if not isinstance(accounts, list):
                print("Warning: turbotable.json should contain a list of account objects.")
                return

            for acc in accounts:
                auth_token = acc.get("auth_token")
                chat_id = acc.get("chat_id")
                createNewChat = acc.get("createNewChat")
                if auth_token and chat_id and createNewChat:
                    TURBOTABLE_ACCOUNTS.append({
                        "auth_token": auth_token,
                        "chat_id": chat_id,
                        "createNewChat": createNewChat,
                    })
            print(f"Successfully loaded {len(TURBOTABLE_ACCOUNTS)} TurboTable accounts.")
    except FileNotFoundError:
        print("Error: turbotable.json not found. API calls will fail.")
    except Exception as e:
        print(f"Error loading turbotable.json: {e}")


def load_turbotable_models():
    """Load TurboTable models from models.json"""
    global TURBOTABLE_MODELS
    try:
        with open("models.json", "r", encoding="utf-8") as f:
            TURBOTABLE_MODELS = json.load(f)
            if not isinstance(TURBOTABLE_MODELS, list):
                TURBOTABLE_MODELS = []
                print("Warning: models.json should contain a list of model objects.")
                return
            print(f"Successfully loaded {len(TURBOTABLE_MODELS)} models.")
    except FileNotFoundError:
        print("Error: models.json not found. Model list will be empty.")
        TURBOTABLE_MODELS = []
    except Exception as e:
        print(f"Error loading models.json: {e}")
        TURBOTABLE_MODELS = []


def get_turbotable_account() -> Optional[TurboTableAccount]:
    """获取下一个可用的TurboTable账户，使用简单轮询机制"""
    global account_index
    
    with account_rotation_lock:
        if not TURBOTABLE_ACCOUNTS:
            return None
            
        # 简单轮询选择下一个账户
        account = TURBOTABLE_ACCOUNTS[account_index % len(TURBOTABLE_ACCOUNTS)]
        account_index = (account_index + 1) % len(TURBOTABLE_ACCOUNTS)
        return account


async def authenticate_client(
    auth: Optional[HTTPAuthorizationCredentials] = Depends(security),
):
    """Authenticate client based on API key in Authorization header"""
    if not VALID_CLIENT_KEYS:
        raise HTTPException(
            status_code=503,
            detail="Service unavailable: Client API keys not configured on server.",
        )

    if not auth or not auth.credentials:
        raise HTTPException(
            status_code=401,
            detail="API key required in Authorization header.",
            headers={"WWW-Authenticate": "Bearer"},
        )

    if auth.credentials not in VALID_CLIENT_KEYS:
        raise HTTPException(status_code=403, detail="Invalid client API key.")


@app.on_event("startup")
async def startup():
    """应用启动时初始化配置"""
    print("Starting TurboTable OpenAI API Adapter server...")
    load_client_api_keys()
    load_turbotable_accounts()
    load_turbotable_models()
    print("Server initialization completed.")


def get_models_list_response() -> ModelList:
    """构建模型列表响应"""
    model_infos = []
    
    for model in TURBOTABLE_MODELS:
        # 从模型名称中提取提供商信息
        name = model.get("name", "")
        owned_by = name.split(":")[0] if ":" in name else "unknown"
        
        model_infos.append(
            ModelInfo(
                id=name,
                created=int(time.time()),
                owned_by=owned_by
            )
        )
    
    return ModelList(data=model_infos)


@app.get("/v1/models", response_model=ModelList)
async def list_v1_models(_: None = Depends(authenticate_client)):
    """List available models - authenticated"""
    return get_models_list_response()


@app.get("/models", response_model=ModelList)
async def list_models_no_auth():
    """List available models without authentication - for client compatibility"""
    return get_models_list_response()


@app.get("/debug")
async def toggle_debug(enable: bool = Query(None)):
    """切换调试模式"""
    global DEBUG_MODE
    if enable is not None:
        DEBUG_MODE = enable
    return {"debug_mode": DEBUG_MODE}


@app.post("/v1/chat/completions")
async def chat_completions(
    request: ChatCompletionRequest, _: None = Depends(authenticate_client)
):
    """创建聊天完成请求，使用TurboTable后端"""
    # 获取模型对象
    model_item = next((m for m in TURBOTABLE_MODELS if m.get("name") == request.model), None)
    if not model_item:
        raise HTTPException(status_code=404, detail=f"Model '{request.model}' not found.")

    if not request.messages:
        raise HTTPException(status_code=400, detail="No messages provided in the request.")
    
    log_debug(f"Processing request for model: {request.model}")
    
    # 构建对话提示
    conversation_prompt = build_conversation_prompt(request.messages)
    log_debug(f"Built conversation prompt: {conversation_prompt[:100]}...")
    
    # 尝试所有账户
    for attempt in range(len(TURBOTABLE_ACCOUNTS)):
        account = get_turbotable_account()
        if not account:
            raise HTTPException(
                status_code=503, 
                detail="No TurboTable accounts available."
            )

        try:
            # 获取临时会话ID
            temp_chat_id = get_turbotable_chat_id(account)
            log_debug(f"Got temporary chat ID: {temp_chat_id}")
            
            # 生成唯一消息ID
            import secrets, string
            message_id = "".join(
                secrets.choice(string.ascii_letters + string.digits) for _ in range(16)
            )
            
            # 构建请求
            payload = {
                "chat_id": f"{temp_chat_id}",
                "message": {
                    "parts": [{"type": "text", "text": conversation_prompt}],
                    "id": message_id,
                    "role": "user",
                    "metadata": {"model": model_item["name"]},
                },
            }

            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
                "Accept-Encoding": "gzip, deflate, br, zstd",
                "Content-Type": "application/json",
                "sec-ch-ua": '"Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
                "Cookie": f"sb-exdfneumdxgoicwzmxjb-auth-token={account['auth_token']}",
            }

            log_debug(f"Sending request to TurboTable API with account ending in ...{account['auth_token'][-4:]}")
            
            response = requests.post(
                "https://turbotable.ai/api/chat",
                data=json.dumps(payload),
                headers=headers,
                stream=True,
                timeout=120.0,
            )
            response.raise_for_status()

            if request.stream:
                log_debug("Returning processed response stream")
                return StreamingResponse(
                    turbotable_stream_generator(response, request.model),
                    media_type="text/event-stream",
                    headers={
                        "Cache-Control": "no-cache",
                        "Connection": "keep-alive",
                        "X-Accel-Buffering": "no",
                    },
                )
            else:
                log_debug("Building non-stream response")
                return build_turbotable_non_stream_response(response, request.model)

        except requests.HTTPError as e:
            status_code = getattr(e.response, "status_code", 500)
            error_detail = getattr(e.response, "text", str(e))
            print(f"TurboTable API error ({status_code}): {error_detail}")
            # 继续尝试下一个账户
        
        except Exception as e:
            print(f"Request error: {e}")
            # 继续尝试下一个账户

    # 所有尝试都失败
    if request.stream:
        return StreamingResponse(
            error_stream_generator("All attempts to contact TurboTable API failed.", 503),
            media_type="text/event-stream",
            status_code=503,
        )
    else:
        raise HTTPException(status_code=503, detail="All attempts to contact TurboTable API failed.")


def turbotable_stream_generator(response, model: str):
    """实时流式响应转换 - TurboTable到OpenAI格式"""
    stream_id = f"chatcmpl-{uuid.uuid4().hex}"
    created_time = int(time.time())

    # 发送初始角色增量
    yield f"data: {StreamResponse(id=stream_id, created=created_time, model=model, choices=[StreamChoice(delta={'role': 'assistant'})]).json()}\n\n"

    buffer = ""
    try:
        for chunk in response.iter_content(chunk_size=1024):
            if not chunk:
                continue
                
            chunk_text = chunk.decode("utf-8")
            log_debug(f"Received chunk: {chunk_text[:100]}..." if len(chunk_text) > 100 else chunk_text)
            buffer += chunk_text

            # 处理缓冲区中的完整行
            while "\n" in buffer:
                line, buffer = buffer.split("\n", 1)
                line = line.strip()

                if not line.startswith("data:"):
                    continue
                    
                data_str = line[5:].strip()
                if not data_str or data_str == "[DONE]":
                    if data_str == "[DONE]":
                        log_debug("Received DONE signal")
                    continue

                try:
                    data = json.loads(data_str)
                    
                    # 处理text-delta事件
                    if data.get("type") == "text-delta" and "delta" in data:
                        content = data["delta"]
                        log_debug(f"Extracted content: {content}")
                        
                        # 包装成OpenAI格式
                        openai_response = StreamResponse(
                            id=stream_id,
                            created=created_time,
                            model=model,
                            choices=[StreamChoice(delta={"content": content})],
                        )
                        response_json = openai_response.json()
                        log_debug(f"Sending response: {response_json[:100]}...")
                        yield f"data: {response_json}\n\n"

                except json.JSONDecodeError as e:
                    log_debug(f"JSON decode error: {e}, data: {data_str[:100]}...")
                    continue
    except Exception as e:
        log_debug(f"Stream processing error: {e}")
        yield f"data: {json.dumps({'error': str(e)})}\n\n"
    finally:
        # 发送完成信号
        log_debug("Sending completion signal")
        yield f"data: {StreamResponse(id=stream_id, created=created_time, model=model, choices=[StreamChoice(delta={}, finish_reason='stop')]).json()}\n\n"
        yield "data: [DONE]\n\n"


def build_turbotable_non_stream_response(response, model: str) -> ChatCompletionResponse:
    """通过累积流数据构建非流式响应"""
    full_content = ""

    buffer = ""
    for chunk in response.iter_content(chunk_size=1024):
        if not chunk:
            continue
            
        buffer += chunk.decode("utf-8")
        
        while "\n" in buffer:
            line, buffer = buffer.split("\n", 1)
            line = line.strip()

            if not line.startswith("data:"):
                continue
                
            data_str = line[5:].strip()
            if not data_str or data_str == "[DONE]":
                continue

            try:
                data = json.loads(data_str)
                if data.get("type") == "text-delta" and "delta" in data:
                    full_content += data["delta"]
            except json.JSONDecodeError:
                continue

    return ChatCompletionResponse(
        model=model,
        choices=[
            ChatCompletionChoice(
                message=ChatMessage(
                    role="assistant",
                    content=full_content
                )
            )
        ],
    )


async def error_stream_generator(error_detail: str, status_code: int):
    """生成错误流响应"""
    yield f'data: {json.dumps({"error": {"message": error_detail, "code": status_code}})}\n\n'
    yield "data: [DONE]\n\n"


def get_turbotable_chat_id(account: TurboTableAccount) -> int:
    """获取TurboTable临时会话ID"""
    url = f"https://turbotable.ai/chat/{account['chat_id']}"
    
    payload = "[null]"
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "Accept": "text/x-component",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Content-Type": "text/plain",
        "next-action": account["createNewChat"],
        "Cookie": f"sb-exdfneumdxgoicwzmxjb-auth-token={account['auth_token']}",
    }
    
    try:
        response = requests.post(url, data=payload, headers=headers).text
        start = response.find('"id":') + 5
        end = response.find(',', start)
        data_id = int(response[start:end])
        return data_id
    except Exception as e:
        print(f"Error getting chat ID: {e}")
        raise


def build_conversation_prompt(messages: List[ChatMessage]) -> str:
    """将多轮对话消息列表拼接成Human/Assistant格式的单一字符串"""
    result = ""
    
    for msg in messages:
        role = "Human" if msg.role.lower() == "user" else "Assistant"
        content = msg.content if isinstance(msg.content, str) else json.dumps(msg.content)
        result += f"\n\n{role}: {content}"
    
    # 如果最后一条消息是用户消息，添加Assistant:前缀引导模型回复
    if messages and messages[-1].role.lower() == "user":
        result += "\n\nAssistant:"
        
    return result


if __name__ == "__main__":
    import uvicorn

    # 设置环境变量以启用调试模式
    if os.environ.get("DEBUG_MODE", "").lower() == "true":
        DEBUG_MODE = True
        print("Debug mode enabled via environment variable")

    if not os.path.exists("turbotable.json"):
        print("Warning: turbotable.json not found. Creating a dummy file.")
        dummy_data = [
            {
                "auth_token": "your_auth_token_here",
                "chat_id": 1,
                "createNewChat": "your_create_new_chat_here",
            }
        ]
        with open("turbotable.json", "w", encoding="utf-8") as f:
            json.dump(dummy_data, f, indent=4)
        print("Created dummy turbotable.json. Please replace with valid TurboTable data.")

    if not os.path.exists("client_api_keys.json"):
        print("Warning: client_api_keys.json not found. Creating a dummy file.")
        dummy_key = f"sk-dummy-{uuid.uuid4().hex}"
        with open("client_api_keys.json", "w", encoding="utf-8") as f:
            json.dump([dummy_key], f, indent=2)
        print(f"Created dummy client_api_keys.json with key: {dummy_key}")

    if not os.path.exists("models.json"):
        print("Warning: models.json not found. Creating a dummy file.")
        dummy_models = [
            {
                "name": "anthropic:claude-sonnet-4",
                "displayName": "Claude Sonnet 4"
            }
        ]
        with open("models.json", "w", encoding="utf-8") as f:
            json.dump(dummy_models, f, indent=4)
        print("Created dummy models.json.")

    load_client_api_keys()
    load_turbotable_accounts()
    load_turbotable_models()

    print("\n--- TurboTable OpenAI API Adapter ---")
    print(f"Debug Mode: {DEBUG_MODE}")
    print("Endpoints:")
    print("  GET  /v1/models (Client API Key Auth)")
    print("  GET  /models (No Auth)")
    print("  POST /v1/chat/completions (Client API Key Auth)")
    print("  GET  /debug?enable=[true|false] (Toggle Debug Mode)")

    print(f"\nClient API Keys: {len(VALID_CLIENT_KEYS)}")
    if TURBOTABLE_ACCOUNTS:
        print(f"TurboTable Accounts: {len(TURBOTABLE_ACCOUNTS)}")
    else:
        print("TurboTable Accounts: None loaded. Check turbotable.json.")
    if TURBOTABLE_MODELS:
        models = sorted([m.get("name", "unknown") for m in TURBOTABLE_MODELS])
        print(f"TurboTable Models: {len(TURBOTABLE_MODELS)}")
        print(f"Available models: {', '.join(models[:5])}{'...' if len(models) > 5 else ''}")
    else:
        print("TurboTable Models: None loaded. Check models.json.")
    print("------------------------------------")

    uvicorn.run(app, host="0.0.0.0", port=50017)